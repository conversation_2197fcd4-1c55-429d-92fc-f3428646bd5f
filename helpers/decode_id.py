from rest_framework_simplejwt.tokens import RefreshToken

def decode_token(refresh_token):
    try:
        # Remove the "Bearer " keyword from the authorization header
        token = refresh_token.replace("Bearer ", "")

        # Decode the refresh token
        refresh_token = RefreshToken(token)
        
        # Extract user ID from the token payload
        user_id = refresh_token.payload['user_id']
        
        return user_id
    except Exception as e:
        # Handle any exceptions, such as invalid token format
        print(f"Error decoding refresh token: {e}")
        return None