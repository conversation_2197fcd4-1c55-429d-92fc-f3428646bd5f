from helpers.notification_helper import send_notification

def send_in_app_notification(data,model,send,url=None):
    user_id = data.get('user_id')
    user_data = model.objects.get(pk=user_id)
    room = str(user_id)
    if user_data.socket_id != '':
        send('notification',data,to=room)
        print('sent inapp notification')
    else:
        message = data.get('message')
        title = data.get('title')
        send_notification(message,title,[user_data.onesignal_player],url)