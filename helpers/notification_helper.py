import onesignal.api_client
from core.settings import ONESIGNAL_APP_ID, ONESIGNAL_API_KEY
import onesignal
from onesignal.api import default_api
from onesignal.model.notification import Notification


configuration = onesignal.Configuration(
    app_key=ONESIGNAL_API_KEY,
    user_key=ONESIGNAL_APP_ID
)


def send_notification(message, heading, player_ids=None,url=None,segments=None, sound=None, image_url=None):
    with onesignal.ApiClient(configuration) as api_client:
        api_instance = default_api.DefaultApi(api_client)

        notification_content = {
            "en": message
        }

        notification_heading = {
            "en": heading
        }

        notification = Notification(
            app_id=ONESIGNAL_APP_ID,
            contents=notification_content,
            headings=notification_heading,
            ios_sound=sound if sound else None,
            android_sound=sound if sound else None,
            big_picture=image_url if image_url else None,
            url=url, 
            is_ios =True,
            is_android=True
        )
        if player_ids:
            notification.include_player_ids = player_ids
        elif segments:
            notification.included_segments = segments
        else:
            notification.included_segments = ["All"]

        try:
            # Send notification
            api_response = api_instance.create_notification(notification)
            print("Notification sent successfully:", api_response)
            return api_response
        except onesignal.ApiException as e:
            print("Exception when calling DefaultApi->create_notification:", e)
            return None


# print(send_notification('Test By Flowkar Devs For Deep Linking','Flowkar',['be2fb777-6602-40a7-88e6-4029a371fcc8']))