from django.urls import path
from .views import *

urlpatterns = [
    path('get-all-profile/',GetAllProfile.as_view()),
    path('like-profile/',LikeProfileView.as_view()),
    path('dislike-profile/',DislikeProfileView.as_view()),
    path('profile-like-inwards/',ProfileLikeInwards.as_view()),
    path('profile-like-outwards/',ProfileLikeOutwards.as_view()),
    path('detailed-profile/',DetailedProfileView.as_view()),
    path('accept-like-profile/',AcceptLikeProfileView.as_view()),
    #Message List
    path('message-list/',MessageListView.as_view()),

    #Admin
    path('get-all-user/',GetAllUserView.as_view()),
    path('get-connected-user/',GetConnectedUserView.as_view()),
    path('user-graph/',UserRegistrationStatsView.as_view()),
    path('get-all-connected-users/', GetAllConnectedUsers.as_view()),
]