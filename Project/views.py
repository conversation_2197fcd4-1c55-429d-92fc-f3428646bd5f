from django.utils import timezone
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response

from Authentication.customer_verifier import CustomAdminJWTAuthentication, CustomJWTAuthentication
from Authentication.models import *
from django.db.models import Q
from Project.models import *
from helpers.decode_id import decode_token
from helpers.pagination import CustomPagination
from helpers.str_to_bool import convert_to_bool
from datetime import datetime,timedelta


# class GetAllProfile(APIView):
#     authentication_classes=[CustomJWTAuthentication]

#     def get(self,request):
#         try:
#             token = request.headers.get('Authorization')
#             user_id = decode_token(token)
#             block_profiles = BlockProfile.objects.filter(user=user_id, blocked_profile__user__is_delete=False)
#             block_profile_ids = [profile.blocked_profile.id for profile in block_profiles]
#             report_profiles = ReportProfile.objects.filter(user=user_id, profile__user__is_delete=False)
#             report_profile_ids = [profile.profile.id for profile in report_profiles]
#             # user_profiles = UserProfile.objects.filter(is_active=True, user__is_delete=False).exclude(id__in=block_profile_ids).exclude(id__in=report_profile_ids)
#             user_profiles = UserProfile.objects.filter(is_active=True, user__is_delete=False).exclude(id__in=block_profile_ids).exclude(id__in=report_profile_ids).exclude(user_id=user_id)
#             response_data = []
#             for profile in user_profiles:
#                 append_obj = {
#                     'id':profile.pk,
#                     'name':profile.full_name,
#                     'profile_image':profile.profile_picture.url if profile.profile_picture else '',
#                     'prefered_smoking':profile.prefered_smoking,
#                     'cleaniness':profile.cleaniness,
#                     'prefered_lease_period':profile.prefered_lease_period,
#                     'pets':profile.is_having_pet,
#                     'about':profile.about,
#                     'personality_type_description':profile.personality_type_description,
#                     'habits_lifestyle':profile.habits_lifestyle,
#                     'living_style':profile.living_style,
#                     'interests_hobbies':profile.interests_hobbies,
#                     'class_standing':profile.class_standing
#                 }
#                 response_data.append(append_obj)
#             return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
#         except Exception as e :
#             return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)





class GetAllProfile(APIView):
    authentication_classes=[CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)

            # Get blocked profiles
            block_profiles = BlockProfile.objects.filter(user=user_id, blocked_profile__user__is_delete=False)
            block_profile_ids = [profile.blocked_profile.id for profile in block_profiles]

            # Get reported profiles
            report_profiles = ReportProfile.objects.filter(user=user_id, profile__user__is_delete=False)
            report_profile_ids = [profile.profile.id for profile in report_profiles]

            # Get liked profiles
            liked_profiles = LikeProfile.objects.filter(user=user_id, liked_profile__user__is_delete=False)
            liked_profile_ids = [profile.liked_profile.id for profile in liked_profiles]

            # Get disliked profiles
            disliked_profiles = DislikeProfile.objects.filter(user=user_id, disliked_profile__user__is_delete=False)
            disliked_profile_ids = [profile.disliked_profile.id for profile in disliked_profiles]


            broken_connections = PastConnection.objects.filter(
                is_break_connection=True
            ).filter(
                Q(from_user_id=user_id) | Q(to_user_id=user_id)
            )

            broken_user_ids = set()
            for conn in broken_connections:
                if str(conn.from_user_id) != str(user_id):
                    broken_user_ids.add(conn.from_user_id)
                if str(conn.to_user_id) != str(user_id):
                    broken_user_ids.add(conn.to_user_id)


            # Filter user profiles excluding blocked, reported, liked, disliked profiles and current user's own profile
            # user_profiles = UserProfile.objects.filter(
            #     is_active=True,
            #     user__is_delete=False
            # ).exclude(
            #     id__in=block_profile_ids
            # ).exclude(
            #     id__in=report_profile_ids
            # ).exclude(
            #     id__in=liked_profile_ids
            # ).exclude(
            #     id__in=disliked_profile_ids
            # ).exclude(
            #     user_id=user_id
            # )
            user_profiles = UserProfile.objects.filter(
                is_active=True,
                user__is_delete=False
            ).exclude(
                id__in=block_profile_ids
            ).exclude(
                id__in=report_profile_ids
            ).exclude(
                id__in=liked_profile_ids
            ).exclude(
                id__in=disliked_profile_ids
            ).exclude(
                user_id__in=broken_user_ids
            ).exclude(
                user_id=user_id
            )
                    
            response_data = []
            for profile in user_profiles:
                append_obj = {
                    'id':profile.pk,
                    'name':profile.full_name,
                    'profile_image':profile.profile_picture.url if profile.profile_picture else '',
                    'prefered_smoking':profile.prefered_smoking,
                    'cleaniness':profile.cleaniness,
                    'prefered_lease_period':profile.prefered_lease_period,
                    'pets':profile.is_having_pet,
                    'about':profile.about,
                    'personality_type_description':profile.personality_type_description,
                    'habits_lifestyle':profile.habits_lifestyle,
                    'living_style':profile.living_style,
                    'interests_hobbies':profile.interests_hobbies,
                    'class_standing':profile.class_standing
                }
                response_data.append(append_obj)
            return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)
        
        
class LikeProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            like_profile_id = request.data.get('like_profile_id')

            try:
                liked_profile = LikeProfile.objects.get(user_id=user_id,liked_profile_id=like_profile_id)
                return Response({'status':False,'message':'Profile Already Liked'},status=status.HTTP_400_BAD_REQUEST)
            except LikeProfile.DoesNotExist:
                LikeProfile.objects.create(user_id=user_id,liked_profile_id=like_profile_id)
                return Response({'status':True,'message':'Profile Liked Successfully'},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)

class DislikeProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            like_profile_id = request.data.get('dislike_profile_id')

            try:
                liked_profile = DislikeProfile.objects.get(user_id=user_id,disliked_profile_id=like_profile_id)
                return Response({'status':False,'message':'Profile Already Disliked'},status=status.HTTP_400_BAD_REQUEST)
            except DislikeProfile.DoesNotExist:
                DislikeProfile.objects.create(user_id=user_id,disliked_profile_id=like_profile_id)
                return Response({'status':True,'message':'Profile Disliked Successfully'},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)
        

class ProfileLikeInwards(APIView):
    authentication_classes=[CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)

            try:
                liked_profiles = LikeProfile.objects.filter(liked_profile__user_id=user_id, user__is_delete=False)
                response_data = []
                for profile in liked_profiles:
                    try:
                        user_profile = UserProfile.objects.get(user_id=profile.user.pk, user__is_delete=False)
                        append_obj = {
                            'id':profile.pk,
                            'name':user_profile.full_name,
                            'to_message_id':profile.user.pk,
                            'profile_image':user_profile.profile_picture.url if user_profile.profile_picture else '',
                            'about':user_profile.about,
                            'year':user_profile.class_standing,
                            'status':profile.is_accepted
                        }
                        response_data.append(append_obj)
                    except UserProfile.DoesNotExist:
                        continue
                return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
            except LikeProfile.DoesNotExist:
                return Response({'status':True,'message':'Data Not Found','data':[]},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)




# class AcceptLikeProfileView(APIView):
#     authentication_classes = [CustomJWTAuthentication]

#     def post(self, request):
#         try:
#             token = request.headers.get('Authorization')
#             user_id = decode_token(token)
#             like_profile_id = request.data.get('like_profile_id')
#             is_accepted = convert_to_bool(request.data.get('is_accepted'))

#             try:
#                 liked_profile = LikeProfile.objects.get(id=like_profile_id, liked_profile__user_id=user_id, user__is_delete=False)
#                 liked_profile.is_accepted = is_accepted
#                 liked_profile.save()

#                 msg = 'Profile Accepted Successfully' if is_accepted else 'Profile Rejected Successfully'
#                 return Response({'status': True, 'message': msg}, status=status.HTTP_200_OK)
#             except LikeProfile.DoesNotExist:
#                 return Response({'status': False, 'message': 'Profile Not Found'}, status=status.HTTP_400_BAD_REQUEST)

#         except Exception as e:
#             return Response({'status': False, 'message': f'Error - {e}'}, status=status.HTTP_400_BAD_REQUEST)



class AcceptLikeProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def post(self, request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)
            like_profile_id = request.data.get('like_profile_id')
            is_accepted = convert_to_bool(request.data.get('is_accepted'))

            try:
                liked_profile = LikeProfile.objects.get(
                    id=like_profile_id,
                    user__is_delete=False
                )
            except LikeProfile.DoesNotExist:
                return Response({'status': False, 'message': 'Profile Not Found'}, status=status.HTTP_400_BAD_REQUEST)

            if is_accepted:
                already_connected = LikeProfile.objects.filter(
                    liked_profile__user_id=user_id,
                    is_accepted=True
                ).exclude(id=like_profile_id).exists()

                if already_connected:
                    return Response({
                        'status': False,
                        'message': 'You already have an active connection with another profile.'
                    }, status=status.HTTP_400_BAD_REQUEST)

                liked_profile.is_accepted = True
                liked_profile.save()
                return Response({'status': True, 'message': 'Profile Accepted Successfully'}, status=status.HTTP_200_OK)

            else:
                PastConnection.objects.create(
                    from_user_id=user_id,
                    to_user_id=liked_profile.user.pk,
                    is_break_connection=True
                )
                
                liked_profile.delete()
                return Response({'status': True, 'message': 'Profile Rejected and Removed Successfully'}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error - {e}'}, status=status.HTTP_400_BAD_REQUEST)




class ProfileLikeOutwards(APIView):
    authentication_classes=[CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)

            try:
                liked_profiles = LikeProfile.objects.filter(user_id=user_id, liked_profile__user__is_delete=False)
                response_data = []
                for profile in liked_profiles:
                    append_obj = {
                        'id':profile.pk,
                        'name':profile.liked_profile.full_name,
                        'year':profile.liked_profile.class_standing,
                        'profile_image':profile.liked_profile.profile_picture.url if profile.liked_profile.profile_picture else '',
                        'about':profile.liked_profile.about,
                        'message_id':profile.liked_profile.user.pk,
                        'status':profile.is_accepted
                    }
                    response_data.append(append_obj)
                
                return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
            except LikeProfile.DoesNotExist:
                return Response({'status':True,'message':'Data Not Found','data':[]},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)
        
class DetailedProfileView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self,request):
        try:
            token = request.headers.get('Authorization')
            user_id = decode_token(token)  
            profile_id = request.query_params.get('profile_id')
            try:
                profile_data = UserProfile.objects.get(pk=profile_id, user__is_delete=False)
                response_data = {
                    'id':profile_data.pk,
                    'profile_image':profile_data.profile_picture.url if profile_data.profile_picture else '',
                    'name':profile_data.full_name,
                    'gender':profile_data.gender,
                    'dob':profile_data.dob,
                    'cleaniness':profile_data.cleaniness,
                    'lease_period':profile_data.prefered_lease_period,
                    'prefered_gender':profile_data.prefered_gender,
                    'prefered_locations':profile_data.prefered_locations,
                    'personality_type_description':profile_data.personality_type_description,
                    'habits_lifestyle':profile_data.habits_lifestyle,
                    'living_style':profile_data.living_style,
                    'interests_hobbies':profile_data.interests_hobbies,
                    'contact_number':profile_data.contact_number, 
                    'is_verified':profile_data.is_verified,     
                    'is_active':profile_data.is_active,              
                    'year':profile_data.class_standing,
                    'about':profile_data.about,
                    'smoker':profile_data.prefered_smoking,
                    'pet':profile_data.is_having_pet,
                    'additional_profiles_images':[i.picture.url for i in UserProfilePictures.objects.filter(user_profile_id=profile_data.pk) if i.picture]
                }
                return Response({'status':True,'message':'Data Found Successfully','data':response_data},status=status.HTTP_200_OK)
            except UserProfile.DoesNotExist:
                return Response({'status':True,'message':'Data Not Found','data':{}},status=status.HTTP_200_OK)
        except Exception as e :
            return Response({'status':False,'message':f'Error -{e}'},status=status.HTTP_400_BAD_REQUEST)

class MessageListView(APIView):
    authentication_classes = [CustomJWTAuthentication]
    pagination_class = CustomPagination
    
    def get(self, request):
        auth_token = request.headers.get('Authorization')
        logged_in_user_id = decode_token(auth_token)
        chat_user_id = request.query_params.get('chat_user_id')

        if not chat_user_id:
            return Response({'status': False, 'message': 'chat_user_id is required.'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            messages = ChatMessage.objects.filter(
                (Q(from_user_id=logged_in_user_id) & Q(to_user_id=chat_user_id)) |
                (Q(from_user_id=chat_user_id) & Q(to_user_id=logged_in_user_id)),
                from_user__is_delete=False,
                to_user__is_delete=False
            ).order_by('-created_at', '-id')

            message_list = []

            for msg in messages:
                created_at_local = timezone.localtime(msg.created_at).isoformat()
                message_data = {
                    'id': msg.id,
                    'type': msg.type,
                    'is_read': msg.is_read,
                    'created_at': created_at_local,
                    'sent_by': msg.from_user.pk
                }

                if msg.type in ['text', 'message']:
                    message_data['message'] = msg.messages

                elif msg.type in ['image', 'voice', 'custom']:
                    message_data['message'] = f'{msg.file}'
                    message_data['file'] = f'{msg.file}'
                message_list.append(message_data)

            paginator = self.pagination_class()
            paginated_message_list = paginator.paginate_queryset(message_list, request)
            return paginator.get_paginated_response({
                'status': True,
                'message': 'Data Found Successfully',
                'data': paginated_message_list
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)
        


class GetConnectedUserView(APIView):
    authentication_classes = [CustomJWTAuthentication]

    def get(self, request):
        try:
            token = request.headers.get('Authorization')
            current_user_id = decode_token(token)

            # Check if current user has an accepted connection
            # connection = LikeProfile.objects.filter(
            #     liked_profile__user__id=current_user_id,
            #     is_accepted=True,
            #     user__is_delete=False
            # ).first()

            connection = LikeProfile.objects.filter(
                Q(user__id=current_user_id) | Q(liked_profile__user__id=current_user_id),
                is_accepted=True,
                user__is_delete=False
            ).first()

            if not connection:
                data = {
                    'is_connected_user': False,
                }
                return Response({'status': True, 'message': 'No active connection found','data':data}, status=status.HTTP_200_OK)

            connected_user = (
                connection.user if connection.user.id != current_user_id else connection.liked_profile.user
            )


            try:
                user_profile = UserProfile.objects.get(user=connected_user)

                response_data = {
                    'user_id': connected_user.id,
                    'is_connected_user': connection.is_accepted,
                    'is_connected_id': connection.id,
                    'email': connected_user.email,
                    'full_name': user_profile.full_name,
                    'dob': user_profile.dob,
                    'gender': user_profile.gender,
                    'prefered_gender': user_profile.prefered_gender,
                    'prefered_smoking': user_profile.prefered_smoking,
                    'cleaniness': user_profile.cleaniness,
                    'is_having_pet': user_profile.is_having_pet,
                    'class_standing': user_profile.class_standing,
                    'habits_lifestyle': user_profile.habits_lifestyle,
                    'living_style': user_profile.living_style,
                    'interests_hobbies': user_profile.interests_hobbies,
                    'about': user_profile.about,
                    'contact_number': user_profile.contact_number,
                    'prefered_lease_period': user_profile.prefered_lease_period,
                    'prefered_locations': user_profile.prefered_locations,
                    'personality_type_description': user_profile.personality_type_description,
                    'is_verified': user_profile.is_verified,
                    'is_active': user_profile.is_active,
                    'updated_at': user_profile.updated_at,
                    'profile_picture': user_profile.profile_picture.url if user_profile.profile_picture else '',
                    'profile_pictures': []
                }

                # Add extra pictures
                profile_pics = UserProfilePictures.objects.filter(user_profile=user_profile)
                for pic in profile_pics:
                    if pic.picture:
                        response_data['profile_pictures'].append({
                            'id': pic.id,
                            'url': pic.picture.url
                        })

                return Response({'status': True, 'message': 'Connected user profile fetched successfully', 'data': response_data}, status=200)

            except UserProfile.DoesNotExist:
                return Response({'status': False, 'message': 'Connected user profile not found'}, status=400)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=400)



class GetAllUserView(APIView):
    authentication_classes = [CustomAdminJWTAuthentication]
    pagination_class = CustomPagination

    def get(self, request):
        try:
            user_id = request.query_params.get('user_id')
            search_query = request.query_params.get('search')

            # Case 1: Specific user detail
            if user_id:
                try:
                    user = User.objects.get(pk=user_id)
                    user_profile = UserProfile.objects.get(user=user)

                    response_data = {
                        'id': user_profile.id,
                        'full_name': user_profile.full_name,
                        'dob': user_profile.dob,
                        'gender': user_profile.gender,
                        'prefered_gender': user_profile.prefered_gender,
                        'prefered_smoking': user_profile.prefered_smoking,
                        'cleaniness': user_profile.cleaniness,
                        'is_having_pet': user_profile.is_having_pet,
                        'class_standing': user_profile.class_standing,
                        'habits_lifestyle': user_profile.habits_lifestyle,
                        'living_style': user_profile.living_style,
                        'interests_hobbies': user_profile.interests_hobbies,
                        'about': user_profile.about,
                        'contact_number': user_profile.contact_number,
                        'prefered_lease_period': user_profile.prefered_lease_period,
                        'prefered_locations': user_profile.prefered_locations,
                        'personality_type_description': user_profile.personality_type_description,
                        'is_verified': user_profile.is_verified,
                        'is_active': user_profile.is_active,
                        'updated_at': user_profile.updated_at,
                        'profile_picture': user_profile.profile_picture.url if user_profile.profile_picture else '',
                        'profile_pictures': []
                    }

                    # Add multiple profile pictures
                    profile_pictures = UserProfilePictures.objects.filter(user_profile=user_profile)
                    for picture in profile_pictures:
                        response_data['profile_pictures'].append({
                            'id': picture.pk,
                            'url': picture.picture.url
                        })

                    return Response({
                        'status': True,
                        'message': 'User profile fetched successfully',
                        'data': response_data
                    }, status=status.HTTP_200_OK)

                except User.DoesNotExist:
                    return Response({'status': False, 'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
                except UserProfile.DoesNotExist:
                    return Response({'status': False, 'message': 'User profile not found'}, status=status.HTTP_400_BAD_REQUEST)

            # Case 2: Paginated list of all users
            users = User.objects.all().order_by('-created_at')

            if search_query:
                users = users.filter(userprofile__full_name__icontains=search_query)

            user_data = []
            for user in users:
                try:
                    profile = UserProfile.objects.get(user=user)
                    full_name = profile.full_name
                    profile_picture = profile.profile_picture.url if profile.profile_picture else ''
                    is_profile_created = True
                except UserProfile.DoesNotExist:
                    full_name = ''
                    profile_picture = ''
                    is_profile_created = False

                user_data.append({
                    'id': user.id,
                    'email': user.email,
                    'full_name': full_name,
                    'profile_picture': profile_picture,
                    'is_delete': user.is_delete,
                    'is_profile_created': is_profile_created,  
                })

            paginator = self.pagination_class()
            paginated_users = paginator.paginate_queryset(user_data, request)
            # Count only users who have profiles created and are not deleted
            users_with_profiles = User.objects.filter(userprofile__isnull=False)
            total_users = users_with_profiles.count()
            total_male = users_with_profiles.filter(userprofile__gender='1').count()
            total_female = users_with_profiles.filter(userprofile__gender='2').count()
            total_other_gender = users_with_profiles.filter(userprofile__gender='3').count()


            return paginator.get_paginated_response({
                'status': True,
                'message': 'Users fetched successfully',
                 'deshboard':{
                    'total_users': total_users,
                    'total_male': total_male,
                    'total_female': total_female,
                    'total_other_gender': total_other_gender
                },
                'data': paginated_users
            })

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class UserRegistrationStatsView(APIView):
    authentication_classes = [CustomAdminJWTAuthentication]

    def get(self, request):
        try:
            start_date_str = request.query_params.get('start_date')
            end_date_str = request.query_params.get('end_date')

            if not start_date_str or not end_date_str:
                return Response({'status': False, 'message': 'start_date and end_date are required in YYYY-MM-DD format.'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({'status': False, 'message': 'Invalid date format. Use YYYY-MM-DD.'}, status=status.HTTP_400_BAD_REQUEST)

            if start_date > end_date:
                return Response({'status': False, 'message': 'start_date must be before or equal to end_date.'}, status=status.HTTP_400_BAD_REQUEST)

            users = User.objects.filter(is_delete=False, created_at__date__gte=start_date, created_at__date__lte=end_date)

            date_count = {}
            for user in users:
                date_str = user.created_at.date().isoformat()
                if date_str in date_count:
                    date_count[date_str] += 1
                else:
                    date_count[date_str] = 1

            current = start_date
            result = []
            while current <= end_date:
                date_str = current.isoformat()
                count = date_count.get(date_str, 0)
                result.append({'date': date_str, 'count': count})
                current += timedelta(days=1)

            return Response({
                'status': True,
                'message': 'User registration stats fetched successfully',
                'data': result
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'status': False, 'message': f'Error --> {e}'}, status=status.HTTP_400_BAD_REQUEST)



class GetAllConnectedUsers(APIView):
    authentication_classes = [CustomAdminJWTAuthentication]
    def get(self, request):
        try:
            connected_users = []

            accepted_likes = LikeProfile.objects.filter(is_accepted=True, user__is_delete=False)

            for like in accepted_likes:
                user_id = str(like.user.id)
                liked_profile = like.liked_profile
                liked_user_id = str(liked_profile.user.id)

                is_broken = PastConnection.objects.filter(
                    Q(from_user_id=user_id, to_user_id=liked_user_id) |
                    Q(from_user_id=liked_user_id, to_user_id=user_id),
                    is_break_connection=True
                ).exists()
                
                from_full_name = UserProfile.objects.get(user=like.user).full_name
                from_user_profile_picture = UserProfile.objects.get(user=like.user).profile_picture.url if UserProfile.objects.get(user=like.user).profile_picture else ''
                if not is_broken:
                    connected_users.append({
                        "from_user_id": int(user_id),
                        "from_user_name": from_full_name,
                        "from_user_email": like.user.email,
                        "from_user_profile_picture": from_user_profile_picture,
                        
                        "to_user_id": int(liked_user_id),
                        "to_user_name": liked_profile.full_name,
                        "to_user_email": liked_profile.user.email,
                        "to_user_profile_picture": liked_profile.profile_picture.url if liked_profile.profile_picture else '' 
                    })

            return Response({"status": True, "connected_users": connected_users}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"status": False, "message": f"Error: {e}"}, status=status.HTTP_400_BAD_REQUEST)
