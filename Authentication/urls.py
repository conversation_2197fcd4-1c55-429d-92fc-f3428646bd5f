from django.urls import path
from .views import *

urlpatterns = [
    path('register/', RegisterUserView.as_view(), name='register'),
    path('login/', LoginUserView.as_view(), name='login'),
    path('forgot-password/', ForgotPasswordView.as_view(), name='forgot-password'),
    path('verify-otp/', VerifyOtpView.as_view(), name='verify-otp'),
    path('set-new-password/', SetNewPasswordView.as_view(), name='set-new-password'),
    path('create-user-profile/', CreateUserProfileView.as_view(), name='create-user-profile'),
    path('user-profile/', UserProfileView.as_view(), name='user-profile'),
    path('edit-profile/', EditProfileView.as_view(), name='edit-profile'),
    path('delete-account/', DeleteAccountView.as_view(), name='delete-account'),
    path('confirm-delete-account/', ConfirmDeleteAccountView.as_view(), name='confirm-delete-account'),
    path('get-selection-menu/', GetSelectionMenuView.as_view(), name='get-selection-menu'),

    # App Data
    path('create-habits-lifestyle/', CreateHabitsLifestyleView.as_view(), name='create-habits-lifestyle'),
    path('create-living-style/', CreateLivingStyleView.as_view(), name='create-living-style'),
    path('create-interests-hobbies/', CreateInterestsHobbiesView.as_view(), name='create-interests-hobbies'),
    path('get-habits-lifestyle/', GetHabitsLifestyleView.as_view(), name='get-habits-lifestyle'),
    path('get-living-style/', GetLivingStyleView.as_view(), name='get-living-style'),
    path('get-interests-hobbies/', GetInterestsHobbiesView.as_view(), name='get-interests-hobbies'),

    # Report Profile
    path('report-profile/', ReportProfileView.as_view(), name='report-profile'),

    # Block Profile
    path('block-profile/', BlockProfileView.as_view(), name='block-profile'),

    #Admin
    path('register-admin/', RegisterAdminView.as_view(), name='register-admin'),
    path('login-admin/', LoginAdminView.as_view(), name='login-admin'),
    path('delete-user-by-admin/', DeleteUserByAdminView.as_view(), name='delete-user-by-admin'),
]

